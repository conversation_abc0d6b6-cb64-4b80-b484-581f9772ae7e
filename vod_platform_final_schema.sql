-- =====================================================
-- VOD PLATFORM - FINAL PRODUCTION SCHEMA
-- =====================================================
-- Single file for immediate NestJS development
-- Run this file to create complete database schema
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- ULID generation function for better performance than UUID
CREATE OR REPLACE FUNCTION generate_ulid() RETURNS TEXT AS $$
DECLARE
    timestamp_ms BIGINT;
    random_bytes BYTEA;
    ulid_chars TEXT := '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
    result TEXT := '';
    i INTEGER;
    value BIGINT;
BEGIN
    timestamp_ms := EXTRACT(EPOCH FROM NOW()) * 1000;
    value := timestamp_ms;
    FOR i IN 1..10 LOOP
        result := substr(ulid_chars, (value % 32) + 1, 1) || result;
        value := value / 32;
    END LOOP;
    random_bytes := gen_random_bytes(10);
    FOR i IN 0..9 LOOP
        value := get_byte(random_bytes, i);
        result := result || substr(ulid_chars, (value % 32) + 1, 1);
        IF i < 9 THEN
            result := result || substr(ulid_chars, ((value / 32) % 32) + 1, 1);
        END IF;
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Auto-update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- REFERENCE TABLES
-- =====================================================

-- Content Ratings (G, PG, R, etc.)
CREATE TABLE content_ratings (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    min_age INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Languages
CREATE TABLE languages (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    native_name VARCHAR(100),
    is_subtitle_available BOOLEAN DEFAULT FALSE,
    is_audio_available BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Genres
CREATE TABLE genres (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Tags/Hashtags
CREATE TABLE content_tags (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color_hex VARCHAR(7),
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Creators (Actors, Directors, etc.)
CREATE TABLE content_creators (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    bio TEXT,
    profile_picture_url VARCHAR(1000),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    -- Search optimization
    search_vector TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', name), 'A') ||
        setweight(to_tsvector('simple', COALESCE(bio, '')), 'B')
    ) STORED
);

-- =====================================================
-- CORE CONTENT TABLES
-- =====================================================

-- Movies
CREATE TABLE movies (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    title VARCHAR(500) NOT NULL,
    original_title VARCHAR(500),
    slug VARCHAR(500) UNIQUE NOT NULL,
    synopsis TEXT,
    duration_minutes INTEGER NOT NULL,
    our_rating DECIMAL(3,1) CHECK (our_rating >= 0 AND our_rating <= 10),
    view_count BIGINT DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    is_adult BOOLEAN DEFAULT FALSE,
    content_rating_id TEXT REFERENCES content_ratings(id) ON DELETE SET NULL,
    release_date DATE,
    release_year INTEGER GENERATED ALWAYS AS (EXTRACT(YEAR FROM release_date)) STORED,
    poster_url VARCHAR(1000),
    backdrop_url VARCHAR(1000),
    keywords TEXT[], -- For SEO and search
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'published', 'archived'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    version INTEGER DEFAULT 1,
    -- Enhanced search with keywords
    search_vector_en TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', title), 'A') ||
        setweight(to_tsvector('simple', COALESCE(original_title, '')), 'B') ||
        setweight(to_tsvector('simple', COALESCE(synopsis, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(array_to_string(keywords, ' '), '')), 'D')
    ) STORED,
    title_trigrams TEXT GENERATED ALWAYS AS (
        lower(title || ' ' || COALESCE(original_title, ''))
    ) STORED
);

-- TV Series
CREATE TABLE tv_series (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    title VARCHAR(500) NOT NULL,
    original_title VARCHAR(500),
    slug VARCHAR(500) UNIQUE NOT NULL,
    synopsis TEXT,
    episode_duration_avg INTEGER,
    our_rating DECIMAL(3,1) CHECK (our_rating >= 0 AND our_rating <= 10),
    view_count BIGINT DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_trending BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    is_adult BOOLEAN DEFAULT FALSE,
    content_rating_id TEXT REFERENCES content_ratings(id) ON DELETE SET NULL,
    release_date DATE,
    release_year INTEGER GENERATED ALWAYS AS (EXTRACT(YEAR FROM release_date)) STORED,
    total_episodes INTEGER DEFAULT 0,
    total_seasons INTEGER DEFAULT 0,
    poster_url VARCHAR(1000),
    backdrop_url VARCHAR(1000),
    keywords TEXT[],
    series_status VARCHAR(20) DEFAULT 'ongoing', -- 'ongoing', 'completed', 'cancelled'
    status VARCHAR(20) DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    version INTEGER DEFAULT 1,
    search_vector_en TSVECTOR GENERATED ALWAYS AS (
        setweight(to_tsvector('simple', title), 'A') ||
        setweight(to_tsvector('simple', COALESCE(original_title, '')), 'B') ||
        setweight(to_tsvector('simple', COALESCE(synopsis, '')), 'C') ||
        setweight(to_tsvector('simple', COALESCE(array_to_string(keywords, ' '), '')), 'D')
    ) STORED,
    title_trigrams TEXT GENERATED ALWAYS AS (
        lower(title || ' ' || COALESCE(original_title, ''))
    ) STORED
);

-- Seasons
CREATE TABLE seasons (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    tv_series_id TEXT NOT NULL REFERENCES tv_series(id) ON DELETE CASCADE,
    season_number INTEGER NOT NULL,
    name VARCHAR(200) NOT NULL,
    overview TEXT,
    episode_count INTEGER DEFAULT 0,
    air_date DATE,
    poster_url VARCHAR(1000),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(tv_series_id, season_number)
);

-- Episodes
CREATE TABLE episodes (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    season_id TEXT NOT NULL REFERENCES seasons(id) ON DELETE CASCADE,
    episode_number INTEGER NOT NULL,
    title VARCHAR(300) NOT NULL,
    overview TEXT,
    duration_minutes INTEGER,
    air_date DATE,
    still_url VARCHAR(1000),
    view_count BIGINT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(season_id, episode_number)
);

-- =====================================================
-- RELATIONSHIP TABLES
-- =====================================================

-- Movie-Genre relationships
CREATE TABLE movie_genres (
    movie_id TEXT REFERENCES movies(id) ON DELETE CASCADE,
    genre_id TEXT REFERENCES genres(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (movie_id, genre_id)
);

-- Movie-Tag relationships
CREATE TABLE movie_tags (
    movie_id TEXT REFERENCES movies(id) ON DELETE CASCADE,
    tag_id TEXT REFERENCES content_tags(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (movie_id, tag_id)
);

-- Movie-Creator relationships
CREATE TABLE movie_creators (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    movie_id TEXT REFERENCES movies(id) ON DELETE CASCADE,
    creator_id TEXT REFERENCES content_creators(id) ON DELETE SET NULL,
    role VARCHAR(100) NOT NULL, -- 'director', 'actor', 'producer', etc.
    character_name VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (movie_id, creator_id, role)
);

-- TV Series relationships (similar structure)
CREATE TABLE tv_series_genres (
    tv_series_id TEXT REFERENCES tv_series(id) ON DELETE CASCADE,
    genre_id TEXT REFERENCES genres(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (tv_series_id, genre_id)
);

CREATE TABLE tv_series_tags (
    tv_series_id TEXT REFERENCES tv_series(id) ON DELETE CASCADE,
    tag_id TEXT REFERENCES content_tags(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (tv_series_id, tag_id)
);

CREATE TABLE tv_series_creators (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    tv_series_id TEXT REFERENCES tv_series(id) ON DELETE CASCADE,
    creator_id TEXT REFERENCES content_creators(id) ON DELETE SET NULL,
    role VARCHAR(100) NOT NULL,
    character_name VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (tv_series_id, creator_id, role)
);

-- =====================================================
-- STREAMING TABLES (SIMPLIFIED)
-- =====================================================

-- Movie Stream Sources (simplified for text-only storage)
CREATE TABLE movie_stream_sources (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    movie_id TEXT NOT NULL REFERENCES movies(id) ON DELETE CASCADE,
    provider_name VARCHAR(100) NOT NULL,
    url TEXT NOT NULL,
    stream_type VARCHAR(20) NOT NULL, -- 'mp4', 'hls', 'embed'
    quality VARCHAR(10) NOT NULL, -- 'SD', 'HD', 'FHD', '4K'
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Episode Stream Sources
CREATE TABLE episode_stream_sources (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    episode_id TEXT NOT NULL REFERENCES episodes(id) ON DELETE CASCADE,
    provider_name VARCHAR(100) NOT NULL,
    url TEXT NOT NULL,
    stream_type VARCHAR(20) NOT NULL,
    quality VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- USER SYSTEM TABLES
-- =====================================================

-- Users
CREATE TABLE users (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- User Profiles
CREATE TABLE user_profiles (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    display_name VARCHAR(100),
    avatar_url VARCHAR(1000),
    bio TEXT,
    date_of_birth DATE,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Movie Watch History (Hash partitioned for scalability)
CREATE TABLE user_movie_watch_history (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    movie_id TEXT NOT NULL REFERENCES movies(id) ON DELETE RESTRICT,
    watched_duration_seconds INTEGER DEFAULT 0,
    total_duration_seconds INTEGER,
    progress_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE
            WHEN total_duration_seconds > 0 THEN
                ROUND((watched_duration_seconds::DECIMAL / total_duration_seconds) * 100, 2)
            ELSE 0
        END
    ) STORED,
    completed BOOLEAN GENERATED ALWAYS AS (progress_percentage >= 90) STORED,
    device_type VARCHAR(20),
    last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, movie_id)
) PARTITION BY HASH (user_id);

-- User Episode Watch History
CREATE TABLE user_episode_watch_history (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    episode_id TEXT NOT NULL REFERENCES episodes(id) ON DELETE RESTRICT,
    watched_duration_seconds INTEGER DEFAULT 0,
    total_duration_seconds INTEGER,
    progress_percentage DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE
            WHEN total_duration_seconds > 0 THEN
                ROUND((watched_duration_seconds::DECIMAL / total_duration_seconds) * 100, 2)
            ELSE 0
        END
    ) STORED,
    completed BOOLEAN GENERATED ALWAYS AS (progress_percentage >= 90) STORED,
    device_type VARCHAR(20),
    last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, episode_id)
) PARTITION BY HASH (user_id);

-- User Favorites
CREATE TABLE user_movie_favorites (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    movie_id TEXT NOT NULL REFERENCES movies(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, movie_id)
) PARTITION BY HASH (user_id);

CREATE TABLE user_series_favorites (
    id TEXT PRIMARY KEY DEFAULT generate_ulid(),
    user_id TEXT NOT NULL,
    tv_series_id TEXT NOT NULL REFERENCES tv_series(id) ON DELETE RESTRICT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, tv_series_id)
) PARTITION BY HASH (user_id);

-- =====================================================
-- CREATE PARTITIONS FOR USER TABLES
-- =====================================================

-- Create 8 partitions for each user table (sufficient for most applications)
DO $$
BEGIN
    FOR i IN 0..7 LOOP
        -- Movie watch history partitions
        EXECUTE format('
            CREATE TABLE user_movie_watch_history_%s
            PARTITION OF user_movie_watch_history
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);

        -- Episode watch history partitions
        EXECUTE format('
            CREATE TABLE user_episode_watch_history_%s
            PARTITION OF user_episode_watch_history
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);

        -- Movie favorites partitions
        EXECUTE format('
            CREATE TABLE user_movie_favorites_%s
            PARTITION OF user_movie_favorites
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);

        -- Series favorites partitions
        EXECUTE format('
            CREATE TABLE user_series_favorites_%s
            PARTITION OF user_series_favorites
            FOR VALUES WITH (modulus 8, remainder %s)', i, i);
    END LOOP;
END $$;

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Core content indexes
CREATE INDEX idx_movies_status ON movies(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_movies_featured ON movies(is_featured, our_rating DESC) WHERE is_featured = TRUE AND status = 'published';
CREATE INDEX idx_movies_trending ON movies(is_trending, view_count DESC) WHERE is_trending = TRUE AND status = 'published';
CREATE INDEX idx_movies_search ON movies USING GIN(search_vector_en) WHERE deleted_at IS NULL;
CREATE INDEX idx_movies_trigrams ON movies USING GIN(title_trigrams gin_trgm_ops) WHERE deleted_at IS NULL;

CREATE INDEX idx_tv_series_status ON tv_series(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_tv_series_featured ON tv_series(is_featured, our_rating DESC) WHERE is_featured = TRUE AND status = 'published';
CREATE INDEX idx_tv_series_search ON tv_series USING GIN(search_vector_en) WHERE deleted_at IS NULL;

-- Relationship indexes
CREATE INDEX idx_movie_genres_movie ON movie_genres(movie_id);
CREATE INDEX idx_movie_genres_genre ON movie_genres(genre_id);
CREATE INDEX idx_movie_tags_movie ON movie_tags(movie_id);
CREATE INDEX idx_movie_tags_tag ON movie_tags(tag_id);
CREATE INDEX idx_movie_creators_movie ON movie_creators(movie_id, role);

CREATE INDEX idx_tv_series_genres_series ON tv_series_genres(tv_series_id);
CREATE INDEX idx_tv_series_tags_series ON tv_series_tags(tv_series_id);

-- Stream source indexes
CREATE INDEX idx_movie_stream_sources_movie ON movie_stream_sources(movie_id, sort_order) WHERE deleted_at IS NULL;
CREATE INDEX idx_movie_stream_sources_active ON movie_stream_sources(is_active, stream_type) WHERE is_active = TRUE;

CREATE INDEX idx_episode_stream_sources_episode ON episode_stream_sources(episode_id, sort_order) WHERE deleted_at IS NULL;

-- User system indexes
CREATE INDEX idx_users_email ON users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_username ON users(username) WHERE deleted_at IS NULL;

-- Soft delete indexes for user data tables
CREATE INDEX idx_user_movie_watch_history_deleted ON user_movie_watch_history(deleted_at) WHERE deleted_at IS NOT NULL;
CREATE INDEX idx_user_episode_watch_history_deleted ON user_episode_watch_history(deleted_at) WHERE deleted_at IS NOT NULL;
CREATE INDEX idx_user_movie_favorites_deleted ON user_movie_favorites(deleted_at) WHERE deleted_at IS NOT NULL;
CREATE INDEX idx_user_series_favorites_deleted ON user_series_favorites(deleted_at) WHERE deleted_at IS NOT NULL;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Auto-update timestamps
CREATE TRIGGER movies_updated_at BEFORE UPDATE ON movies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER tv_series_updated_at BEFORE UPDATE ON tv_series
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER seasons_updated_at BEFORE UPDATE ON seasons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER episodes_updated_at BEFORE UPDATE ON episodes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER content_tags_updated_at BEFORE UPDATE ON content_tags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER content_creators_updated_at BEFORE UPDATE ON content_creators
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Tag usage count trigger
CREATE OR REPLACE FUNCTION update_tag_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE content_tags SET usage_count = usage_count + 1 WHERE id = NEW.tag_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE content_tags SET usage_count = GREATEST(usage_count - 1, 0) WHERE id = OLD.tag_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER movie_tags_usage_count
    AFTER INSERT OR DELETE ON movie_tags
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_count();

CREATE TRIGGER tv_series_tags_usage_count
    AFTER INSERT OR DELETE ON tv_series_tags
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_count();

-- =====================================================
-- ESSENTIAL FUNCTIONS FOR API
-- =====================================================

-- Search movies function
CREATE OR REPLACE FUNCTION search_movies(
    query_text TEXT,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
) RETURNS TABLE (
    id TEXT,
    title VARCHAR(500),
    synopsis TEXT,
    poster_url VARCHAR(1000),
    our_rating DECIMAL(3,1),
    view_count BIGINT,
    keywords TEXT[],
    rank REAL
) AS $$
DECLARE
    search_query TSQUERY;
BEGIN
    search_query := plainto_tsquery('english', query_text);

    RETURN QUERY
    SELECT
        m.id, m.title, m.synopsis, m.poster_url, m.our_rating, m.view_count, m.keywords,
        ts_rank_cd(m.search_vector_en, search_query) as rank
    FROM movies m
    WHERE m.search_vector_en @@ search_query
       OR similarity(m.title_trigrams, lower(query_text)) > 0.3
    ORDER BY
        ts_rank_cd(m.search_vector_en, search_query) DESC,
        similarity(m.title_trigrams, lower(query_text)) DESC,
        m.view_count DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql STABLE;

-- Safe content deletion function
CREATE OR REPLACE FUNCTION safe_delete_movie(movie_id_param TEXT)
RETURNS TABLE (
    can_delete BOOLEAN,
    user_dependencies INTEGER,
    message TEXT
) AS $$
DECLARE
    watch_count INTEGER;
    favorite_count INTEGER;
    total_dependencies INTEGER;
BEGIN
    -- Check user dependencies
    SELECT COUNT(*) INTO watch_count
    FROM user_movie_watch_history
    WHERE movie_id = movie_id_param AND deleted_at IS NULL;

    SELECT COUNT(*) INTO favorite_count
    FROM user_movie_favorites
    WHERE movie_id = movie_id_param AND deleted_at IS NULL;

    total_dependencies := watch_count + favorite_count;

    RETURN QUERY SELECT
        (total_dependencies = 0) as can_delete,
        total_dependencies as user_dependencies,
        CASE
            WHEN total_dependencies = 0 THEN 'Safe to delete - no user dependencies'
            ELSE format('Cannot delete - %s user dependencies found (watch history: %s, favorites: %s)',
                       total_dependencies, watch_count, favorite_count)
        END as message;
END;
$$ LANGUAGE plpgsql;

-- Soft delete user data function
CREATE OR REPLACE FUNCTION soft_delete_user_data(
    table_name TEXT,
    user_id_value TEXT
) RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    EXECUTE format('
        UPDATE %I
        SET deleted_at = NOW()
        WHERE user_id = $1 AND deleted_at IS NULL', table_name)
    USING user_id_value;

    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SAMPLE DATA FOR DEVELOPMENT
-- =====================================================

-- Content ratings
INSERT INTO content_ratings (name, description, min_age) VALUES
('G', 'General Audiences', 0),
('PG', 'Parental Guidance Suggested', 0),
('PG-13', 'Parents Strongly Cautioned', 13),
('R', 'Restricted', 17),
('TV-MA', 'Mature Audience Only', 17);

-- Languages
INSERT INTO languages (name, code, native_name, is_subtitle_available, is_audio_available) VALUES
('English', 'en', 'English', TRUE, TRUE),
('Vietnamese', 'vi', 'Tiếng Việt', TRUE, TRUE),
('Spanish', 'es', 'Español', TRUE, TRUE),
('French', 'fr', 'Français', TRUE, TRUE),
('Japanese', 'ja', '日本語', TRUE, TRUE);

-- Genres
INSERT INTO genres (name, slug, sort_order) VALUES
('Action', 'action', 1),
('Comedy', 'comedy', 2),
('Drama', 'drama', 3),
('Horror', 'horror', 4),
('Romance', 'romance', 5),
('Sci-Fi', 'sci-fi', 6),
('Thriller', 'thriller', 7),
('Animation', 'animation', 8);

-- Content tags
INSERT INTO content_tags (name, slug, description, color_hex) VALUES
('Blockbuster', 'blockbuster', 'High-budget mainstream movies', '#FF6B6B'),
('Indie', 'indie', 'Independent films', '#4ECDC4'),
('Award Winner', 'award-winner', 'Award-winning content', '#FFD93D'),
('Trending', 'trending', 'Currently trending content', '#6BCF7F'),
('New Release', 'new-release', 'Recently released content', '#FF8B94'),
('Binge Watch', 'binge-watch', 'Perfect for binge watching', '#B4A7D6'),
('Feel Good', 'feel-good', 'Uplifting and positive content', '#FFAAA5'),
('Superhero', 'superhero', 'Superhero themed content', '#1D3557');

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'VOD PLATFORM DATABASE SCHEMA CREATED!';
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'Ready for NestJS development';
    RAISE NOTICE 'Tables: % created', (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public');
    RAISE NOTICE 'Functions: search_movies(), safe_delete_movie() available';
    RAISE NOTICE 'Sample data: Content ratings, languages, genres, tags loaded';
    RAISE NOTICE 'SAFETY: Production-ready CASCADE DELETE constraints applied';
    RAISE NOTICE 'SAFETY: User data protected with RESTRICT constraints';
    RAISE NOTICE 'SAFETY: Soft delete support added to user tables';
    RAISE NOTICE '==============================================';
END $$;
